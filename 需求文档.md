# 物流管理系统需求文档

## 1. 系统概述

本系统为物流运输管理平台，主要包含管理端功能，涵盖系统管理、运力管理、调度管理、仓储管理、货物配载、用户中心和清分结算等核心业务模块。

## 2. 功能需求

### 2.1 基础功能模块

#### 2.1.1 系统管理
- **菜单管理**：系统菜单的增删改查、层级管理
- **字典管理**：系统字典数据维护
- **用户管理**：用户账号的创建、编辑、删除、状态管理
- **角色管理**：角色的创建、编辑、删除
- **角色权限管理**：
  - 数据权限：控制用户可访问的数据范围
  - 菜单权限：控制用户可访问的功能菜单
- **组织管理**：组织架构的维护和管理

#### 2.1.2 移动管理
- **菜单管理**：移动端菜单配置
- **功能管理**：移动端功能模块管理
- **权限分配**：支持按角色分配移动端菜单及功能权限

### 2.2 业务功能模块

#### 2.2.1 运力管理

**自有运力**
- **车辆管理**：自有车辆信息的录入、编辑、查询
- **司机管理**：自有司机信息的录入、编辑、查询

**合作运力**
- **车辆管理**：合作方车辆信息管理
- **司机管理**：合作方司机信息管理
- **账户信息创建**：为合作方创建账户信息，用于后续清分结算

#### 2.2.2 调度管理

- **人车绑定**：司机与车辆的绑定关系管理
- **创建车次**：单个车次的创建和管理
- **批量创建车次**：支持批量创建多个车次

#### 2.2.3 落地接车管理

**导入模版功能**
- 支持通过手工录入和Excel导入运单信息
- 导入内容包括：运单信息、货物重量、货物方数、货物名称、收发件人信息、车辆信息、司机信息

**接口信息获取功能**
- 从上游TMS系统获取运单相关信息
- 获取内容包括：运单信息、货物重量、货物方数、货物名称、收发件人信息、车辆信息、司机信息

**单号生成**
- 生成附单单号（参考jihuo-api单号生成逻辑）

**到车确认**
- 提供到车确认功能
- 自动统计各区各市货物数量

#### 2.2.4 仓储管理

**入库信息**
- 落地接车信息导入或录入后自动生成入库信息

**出库信息**
- 货物配载到车次后预生成出库信息
- 车次确认发车后生成正式出库信息

#### 2.2.5 货物分发

- **按预分发信息分发**：将货物分发至指定调度
- **目的地判断**：分发时需判断货物收货地址是否在分发调度负责区域内
- **坐标获取**：参考jihuo-zy中按距离判断运费逻辑，通过地址文字获取坐标信息

#### 2.2.6 货物配载

- **货物分发**：将已入库货物分发到已创建的车次
- **货物规格与车辆规格判断**：判定货物总规格是否超过车辆可运载范围
- **目的地判断**：配载车次需判断货物是否为同一目的地
- **坐标获取**：参考jihuo-zy中按距离判断运费逻辑，通过地址文字获取坐标信息
- **撤单功能**：运力超时未接单时，调度可撤回运单重新分配

#### 2.2.7 用户中心

**认证审核**
- 展示移动端申请的用户信息
- 认证信息存储到独立的认证表中

**审核流程**
- 参考现有项目认证审核逻辑实现

#### 2.2.8 清分结算

- **平台结算**：平台方结算管理
- **合作方结算**：合作运力方结算管理

## 3. 技术要求

- 系统需支持与上游TMS系统的接口对接
- 需要集成地址坐标获取功能
- 参考现有jihuo-api和jihuo-zy项目的相关逻辑实现
- 支持Excel文件导入导出功能
- 需要完善的权限控制机制

## 4. 数据要求

- 建立独立的认证信息表
- 支持运单、货物、车辆、司机等核心业务数据管理
- 需要完整的操作日志记录
- 支持数据权限控制

## 5. 非功能性需求

### 5.1 性能要求
- 系统响应时间不超过3秒
- 支持并发用户数不少于100人
- 数据导入处理能力不少于10000条/分钟

### 5.2 安全要求
- 用户身份认证和授权
- 数据传输加密
- 操作日志审计
- 敏感数据脱敏处理

### 5.3 可用性要求
- 系统可用性不低于99.5%
- 支持7×24小时运行
- 具备故障恢复能力

### 5.4 兼容性要求
- 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- 移动端支持iOS和Android系统
- 支持常见的Excel文件格式导入导出