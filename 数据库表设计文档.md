# 物流管理系统数据库设计文档

## 1. 数据库设计概述

### 1.1 设计原则
- 遵循第三范式，减少数据冗余
- 合理使用索引，提高查询性能
- 统一字段命名规范
- 预留扩展字段，便于后续功能扩展

### 1.2 命名规范
- 表名：小写字母，下划线分隔，如 `sys_user`
- 字段名：小写字母，下划线分隔，如 `user_name`
- 主键：统一使用 `id`
- 外键：关联表名_id，如 `user_id`
- 时间字段：`create_time`、`update_time`

## 2. 系统管理模块表设计

### 2.1 用户表 (sys_user)
```sql
CREATE TABLE `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `org_id` bigint DEFAULT NULL COMMENT '组织ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='用户表';
```

### 2.2 角色表 (sys_role)
```sql
CREATE TABLE `sys_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `data_scope` tinyint DEFAULT '1' COMMENT '数据权限：1-全部，2-本部门，3-本部门及下级，4-仅本人',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) ENGINE=InnoDB COMMENT='角色表';
```

### 2.3 菜单表 (sys_menu)
```sql
CREATE TABLE `sys_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `menu_code` varchar(50) DEFAULT NULL COMMENT '菜单编码',
  `menu_type` tinyint NOT NULL COMMENT '菜单类型：1-目录，2-菜单，3-按钮',
  `path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `component` varchar(200) DEFAULT NULL COMMENT '组件路径',
  `permission` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `visible` tinyint DEFAULT '1' COMMENT '是否显示：0-隐藏，1-显示',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB COMMENT='菜单表';
```

### 2.4 用户角色关联表 (sys_user_role)
```sql
CREATE TABLE `sys_user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`,`role_id`)
) ENGINE=InnoDB COMMENT='用户角色关联表';
```

### 2.5 角色菜单关联表 (sys_role_menu)
```sql
CREATE TABLE `sys_role_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_menu` (`role_id`,`menu_id`)
) ENGINE=InnoDB COMMENT='角色菜单关联表';
```

### 2.6 组织表 (sys_org)
```sql
CREATE TABLE `sys_org` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '组织ID',
  `parent_id` bigint DEFAULT '0' COMMENT '父组织ID',
  `org_name` varchar(50) NOT NULL COMMENT '组织名称',
  `org_code` varchar(50) DEFAULT NULL COMMENT '组织编码',
  `org_type` tinyint DEFAULT '1' COMMENT '组织类型：1-公司，2-部门',
  `leader` varchar(50) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB COMMENT='组织表';
```

### 2.7 字典表 (sys_dict)
```sql
CREATE TABLE `sys_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典ID',
  `dict_name` varchar(100) NOT NULL COMMENT '字典名称',
  `dict_code` varchar(100) NOT NULL COMMENT '字典编码',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dict_code` (`dict_code`)
) ENGINE=InnoDB COMMENT='字典表';
```

### 2.8 字典项表 (sys_dict_item)
```sql
CREATE TABLE `sys_dict_item` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典项ID',
  `dict_id` bigint NOT NULL COMMENT '字典ID',
  `item_text` varchar(100) NOT NULL COMMENT '字典项文本',
  `item_value` varchar(100) NOT NULL COMMENT '字典项值',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_dict_id` (`dict_id`)
) ENGINE=InnoDB COMMENT='字典项表';
```

## 3. 移动管理模块表设计

### 3.1 移动端菜单表 (mobile_menu)
```sql
CREATE TABLE `mobile_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `menu_code` varchar(50) DEFAULT NULL COMMENT '菜单编码',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB COMMENT='移动端菜单表';
```

### 3.2 移动端功能表 (mobile_function)
```sql
CREATE TABLE `mobile_function` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '功能ID',
  `function_name` varchar(50) NOT NULL COMMENT '功能名称',
  `function_code` varchar(50) NOT NULL COMMENT '功能编码',
  `description` varchar(200) DEFAULT NULL COMMENT '功能描述',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_function_code` (`function_code`)
) ENGINE=InnoDB COMMENT='移动端功能表';
```

## 4. 运力管理模块表设计

### 4.1 车辆表 (vehicle)
```sql
CREATE TABLE `vehicle` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '车辆ID',
  `license_plate` varchar(20) NOT NULL COMMENT '车牌号',
  `vehicle_type` tinyint NOT NULL COMMENT '车辆类型：1-自有，2-合作',
  `vehicle_model` varchar(50) DEFAULT NULL COMMENT '车型',
  `load_capacity` decimal(10,2) DEFAULT NULL COMMENT '载重量(吨)',
  `volume_capacity` decimal(10,2) DEFAULT NULL COMMENT '容积(立方米)',
  `vehicle_length` decimal(5,2) DEFAULT NULL COMMENT '车长(米)',
  `vehicle_width` decimal(5,2) DEFAULT NULL COMMENT '车宽(米)',
  `vehicle_height` decimal(5,2) DEFAULT NULL COMMENT '车高(米)',
  `partner_id` bigint DEFAULT NULL COMMENT '合作方ID',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-停用，1-正常，2-维修',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_license_plate` (`license_plate`),
  KEY `idx_vehicle_type` (`vehicle_type`),
  KEY `idx_partner_id` (`partner_id`)
) ENGINE=InnoDB COMMENT='车辆表';
```

### 4.2 司机表 (driver)
```sql
CREATE TABLE `driver` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '司机ID',
  `driver_name` varchar(50) NOT NULL COMMENT '司机姓名',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `license_number` varchar(20) DEFAULT NULL COMMENT '驾驶证号',
  `license_type` varchar(10) DEFAULT NULL COMMENT '准驾车型',
  `license_expire_date` date DEFAULT NULL COMMENT '驾驶证到期日期',
  `driver_type` tinyint NOT NULL COMMENT '司机类型：1-自有，2-合作',
  `partner_id` bigint DEFAULT NULL COMMENT '合作方ID',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-停用，1-正常',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_driver_type` (`driver_type`),
  KEY `idx_partner_id` (`partner_id`)
) ENGINE=InnoDB COMMENT='司机表';
```

### 4.3 合作方账户表 (partner_account)
```sql
CREATE TABLE `partner_account` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `partner_name` varchar(100) NOT NULL COMMENT '合作方名称',
  `partner_code` varchar(50) DEFAULT NULL COMMENT '合作方编码',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `tax_number` varchar(50) DEFAULT NULL COMMENT '税号',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-停用，1-正常',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_partner_code` (`partner_code`)
) ENGINE=InnoDB COMMENT='合作方账户表';
```

## 5. 调度管理模块表设计

### 5.1 司机车辆绑定表 (driver_vehicle_bind)
```sql
CREATE TABLE `driver_vehicle_bind` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '绑定ID',
  `driver_id` bigint NOT NULL COMMENT '司机ID',
  `vehicle_id` bigint NOT NULL COMMENT '车辆ID',
  `bind_date` date NOT NULL COMMENT '绑定日期',
  `unbind_date` date DEFAULT NULL COMMENT '解绑日期',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-已解绑，1-已绑定',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_driver_id` (`driver_id`),
  KEY `idx_vehicle_id` (`vehicle_id`)
) ENGINE=InnoDB COMMENT='司机车辆绑定表';
```

### 5.2 车次表 (trip)
```sql
CREATE TABLE `trip` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '车次ID',
  `trip_no` varchar(50) NOT NULL COMMENT '车次编号',
  `driver_id` bigint NOT NULL COMMENT '司机ID',
  `vehicle_id` bigint NOT NULL COMMENT '车辆ID',
  `departure_city` varchar(50) DEFAULT NULL COMMENT '出发城市',
  `destination_city` varchar(50) DEFAULT NULL COMMENT '目的地城市',
  `departure_address` varchar(200) DEFAULT NULL COMMENT '出发地址',
  `destination_address` varchar(200) DEFAULT NULL COMMENT '目的地地址',
  `planned_departure_time` datetime DEFAULT NULL COMMENT '计划出发时间',
  `actual_departure_time` datetime DEFAULT NULL COMMENT '实际出发时间',
  `planned_arrival_time` datetime DEFAULT NULL COMMENT '计划到达时间',
  `actual_arrival_time` datetime DEFAULT NULL COMMENT '实际到达时间',
  `trip_status` tinyint DEFAULT '1' COMMENT '车次状态：1-待发车，2-运输中，3-已到达，4-已完成',
  `total_weight` decimal(10,2) DEFAULT '0.00' COMMENT '总重量(吨)',
  `total_volume` decimal(10,2) DEFAULT '0.00' COMMENT '总体积(立方米)',
  `freight_amount` decimal(10,2) DEFAULT '0.00' COMMENT '运费金额',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trip_no` (`trip_no`),
  KEY `idx_driver_id` (`driver_id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_trip_status` (`trip_status`)
) ENGINE=InnoDB COMMENT='车次表';
```

## 6. 落地接车管理模块表设计

### 6.1 运单表 (waybill)
```sql
CREATE TABLE `waybill` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '运单ID',
  `waybill_no` varchar(50) NOT NULL COMMENT '运单号',
  `attachment_no` varchar(50) DEFAULT NULL COMMENT '附单号',
  `sender_name` varchar(100) DEFAULT NULL COMMENT '发件人姓名',
  `sender_phone` varchar(20) DEFAULT NULL COMMENT '发件人电话',
  `sender_address` varchar(200) DEFAULT NULL COMMENT '发件人地址',
  `receiver_name` varchar(100) DEFAULT NULL COMMENT '收件人姓名',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收件人电话',
  `receiver_address` varchar(200) DEFAULT NULL COMMENT '收件人地址',
  `receiver_city` varchar(50) DEFAULT NULL COMMENT '收货城市',
  `receiver_district` varchar(50) DEFAULT NULL COMMENT '收货区县',
  `cargo_name` varchar(200) DEFAULT NULL COMMENT '货物名称',
  `cargo_weight` decimal(10,2) DEFAULT NULL COMMENT '货物重量(公斤)',
  `cargo_volume` decimal(10,2) DEFAULT NULL COMMENT '货物体积(立方米)',
  `cargo_quantity` int DEFAULT NULL COMMENT '货物数量',
  `cargo_value` decimal(10,2) DEFAULT NULL COMMENT '货物价值',
  `freight_amount` decimal(10,2) DEFAULT NULL COMMENT '运费金额',
  `payment_method` tinyint DEFAULT NULL COMMENT '付款方式：1-现付，2-到付，3-月结',
  `waybill_status` tinyint DEFAULT '1' COMMENT '运单状态：1-待处理，2-已入库，3-已分发，4-已配载，5-运输中，6-已签收',
  `arrival_vehicle_id` bigint DEFAULT NULL COMMENT '到车车辆ID',
  `arrival_driver_id` bigint DEFAULT NULL COMMENT '到车司机ID',
  `arrival_time` datetime DEFAULT NULL COMMENT '到车时间',
  `source_type` tinyint DEFAULT '1' COMMENT '数据来源：1-手工录入，2-Excel导入，3-TMS接口',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_waybill_no` (`waybill_no`),
  KEY `idx_waybill_status` (`waybill_status`),
  KEY `idx_receiver_city` (`receiver_city`),
  KEY `idx_arrival_time` (`arrival_time`)
) ENGINE=InnoDB COMMENT='运单表';
```

### 6.2 到车确认表 (arrival_confirm)
```sql
CREATE TABLE `arrival_confirm` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '确认ID',
  `vehicle_id` bigint NOT NULL COMMENT '车辆ID',
  `driver_id` bigint NOT NULL COMMENT '司机ID',
  `arrival_time` datetime NOT NULL COMMENT '到车时间',
  `total_waybills` int DEFAULT '0' COMMENT '运单总数',
  `total_weight` decimal(10,2) DEFAULT '0.00' COMMENT '总重量(公斤)',
  `total_volume` decimal(10,2) DEFAULT '0.00' COMMENT '总体积(立方米)',
  `confirm_status` tinyint DEFAULT '1' COMMENT '确认状态：1-已确认，0-已取消',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_vehicle_id` (`vehicle_id`),
  KEY `idx_driver_id` (`driver_id`),
  KEY `idx_arrival_time` (`arrival_time`)
) ENGINE=InnoDB COMMENT='到车确认表';
```

## 7. 仓储管理模块表设计

### 7.1 入库记录表 (inbound_record)
```sql
CREATE TABLE `inbound_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '入库记录ID',
  `inbound_no` varchar(50) NOT NULL COMMENT '入库单号',
  `waybill_id` bigint NOT NULL COMMENT '运单ID',
  `waybill_no` varchar(50) NOT NULL COMMENT '运单号',
  `cargo_name` varchar(200) DEFAULT NULL COMMENT '货物名称',
  `cargo_weight` decimal(10,2) DEFAULT NULL COMMENT '货物重量(公斤)',
  `cargo_volume` decimal(10,2) DEFAULT NULL COMMENT '货物体积(立方米)',
  `cargo_quantity` int DEFAULT NULL COMMENT '货物数量',
  `warehouse_area` varchar(50) DEFAULT NULL COMMENT '仓储区域',
  `storage_location` varchar(100) DEFAULT NULL COMMENT '存储位置',
  `inbound_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '入库时间',
  `inbound_status` tinyint DEFAULT '1' COMMENT '入库状态：1-已入库，2-已分发，3-已出库',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_inbound_no` (`inbound_no`),
  KEY `idx_waybill_id` (`waybill_id`),
  KEY `idx_inbound_status` (`inbound_status`)
) ENGINE=InnoDB COMMENT='入库记录表';
```

### 7.2 出库记录表 (outbound_record)
```sql
CREATE TABLE `outbound_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '出库记录ID',
  `outbound_no` varchar(50) NOT NULL COMMENT '出库单号',
  `inbound_record_id` bigint NOT NULL COMMENT '入库记录ID',
  `trip_id` bigint NOT NULL COMMENT '车次ID',
  `waybill_id` bigint NOT NULL COMMENT '运单ID',
  `waybill_no` varchar(50) NOT NULL COMMENT '运单号',
  `cargo_name` varchar(200) DEFAULT NULL COMMENT '货物名称',
  `cargo_weight` decimal(10,2) DEFAULT NULL COMMENT '货物重量(公斤)',
  `cargo_volume` decimal(10,2) DEFAULT NULL COMMENT '货物体积(立方米)',
  `cargo_quantity` int DEFAULT NULL COMMENT '货物数量',
  `outbound_time` datetime DEFAULT NULL COMMENT '出库时间',
  `outbound_type` tinyint DEFAULT '1' COMMENT '出库类型：1-预出库，2-正式出库',
  `outbound_status` tinyint DEFAULT '1' COMMENT '出库状态：1-已出库，2-运输中，3-已签收',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_outbound_no` (`outbound_no`),
  KEY `idx_inbound_record_id` (`inbound_record_id`),
  KEY `idx_trip_id` (`trip_id`),
  KEY `idx_waybill_id` (`waybill_id`)
) ENGINE=InnoDB COMMENT='出库记录表';
```

## 8. 货物分发模块表设计

### 8.1 货物分发表 (cargo_distribution)
```sql
CREATE TABLE `cargo_distribution` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分发ID',
  `distribution_no` varchar(50) NOT NULL COMMENT '分发单号',
  `inbound_record_id` bigint NOT NULL COMMENT '入库记录ID',
  `waybill_id` bigint NOT NULL COMMENT '运单ID',
  `dispatcher_id` bigint NOT NULL COMMENT '调度员ID',
  `receiver_city` varchar(50) DEFAULT NULL COMMENT '收货城市',
  `receiver_district` varchar(50) DEFAULT NULL COMMENT '收货区县',
  `receiver_address` varchar(200) DEFAULT NULL COMMENT '收货地址',
  `receiver_longitude` decimal(10,6) DEFAULT NULL COMMENT '收货地址经度',
  `receiver_latitude` decimal(10,6) DEFAULT NULL COMMENT '收货地址纬度',
  `distribution_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分发时间',
  `distribution_status` tinyint DEFAULT '1' COMMENT '分发状态：1-已分发，2-已配载',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_distribution_no` (`distribution_no`),
  KEY `idx_inbound_record_id` (`inbound_record_id`),
  KEY `idx_waybill_id` (`waybill_id`),
  KEY `idx_dispatcher_id` (`dispatcher_id`)
) ENGINE=InnoDB COMMENT='货物分发表';
```

### 8.2 调度区域表 (dispatch_area)
```sql
CREATE TABLE `dispatch_area` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '区域ID',
  `dispatcher_id` bigint NOT NULL COMMENT '调度员ID',
  `area_name` varchar(100) NOT NULL COMMENT '区域名称',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `area_polygon` text COMMENT '区域多边形坐标',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_dispatcher_id` (`dispatcher_id`),
  KEY `idx_city_district` (`city`,`district`)
) ENGINE=InnoDB COMMENT='调度区域表';
```

## 9. 货物配载模块表设计

### 9.1 货物配载表 (cargo_loading)
```sql
CREATE TABLE `cargo_loading` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配载ID',
  `loading_no` varchar(50) NOT NULL COMMENT '配载单号',
  `trip_id` bigint NOT NULL COMMENT '车次ID',
  `distribution_id` bigint NOT NULL COMMENT '分发ID',
  `waybill_id` bigint NOT NULL COMMENT '运单ID',
  `cargo_weight` decimal(10,2) DEFAULT NULL COMMENT '货物重量(公斤)',
  `cargo_volume` decimal(10,2) DEFAULT NULL COMMENT '货物体积(立方米)',
  `loading_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '配载时间',
  `loading_status` tinyint DEFAULT '1' COMMENT '配载状态：1-已配载，2-已发车，3-已撤单',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_loading_no` (`loading_no`),
  KEY `idx_trip_id` (`trip_id`),
  KEY `idx_distribution_id` (`distribution_id`),
  KEY `idx_waybill_id` (`waybill_id`)
) ENGINE=InnoDB COMMENT='货物配载表';
```

### 9.2 撤单记录表 (order_cancellation)
```sql
CREATE TABLE `order_cancellation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '撤单ID',
  `cancellation_no` varchar(50) NOT NULL COMMENT '撤单号',
  `loading_id` bigint NOT NULL COMMENT '配载ID',
  `trip_id` bigint NOT NULL COMMENT '车次ID',
  `waybill_id` bigint NOT NULL COMMENT '运单ID',
  `cancellation_reason` varchar(200) DEFAULT NULL COMMENT '撤单原因',
  `cancellation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '撤单时间',
  `timeout_minutes` int DEFAULT NULL COMMENT '超时分钟数',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_cancellation_no` (`cancellation_no`),
  KEY `idx_loading_id` (`loading_id`),
  KEY `idx_trip_id` (`trip_id`),
  KEY `idx_waybill_id` (`waybill_id`)
) ENGINE=InnoDB COMMENT='撤单记录表';
```

## 10. 用户中心模块表设计

### 10.1 用户认证表 (user_authentication)
```sql
CREATE TABLE `user_authentication` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '认证ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `id_card` varchar(18) NOT NULL COMMENT '身份证号',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `id_card_front_url` varchar(200) DEFAULT NULL COMMENT '身份证正面照片',
  `id_card_back_url` varchar(200) DEFAULT NULL COMMENT '身份证背面照片',
  `face_photo_url` varchar(200) DEFAULT NULL COMMENT '人脸照片',
  `apply_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `audit_status` tinyint DEFAULT '0' COMMENT '审核状态：0-待审核，1-审核通过，2-审核拒绝',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_by` bigint DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_id_card` (`id_card`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_audit_status` (`audit_status`)
) ENGINE=InnoDB COMMENT='用户认证表';
```

## 11. 清分结算模块表设计

### 11.1 平台结算表 (platform_settlement)
```sql
CREATE TABLE `platform_settlement` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '结算ID',
  `settlement_no` varchar(50) NOT NULL COMMENT '结算单号',
  `settlement_period` varchar(20) NOT NULL COMMENT '结算周期',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `total_orders` int DEFAULT '0' COMMENT '订单总数',
  `total_amount` decimal(12,2) DEFAULT '0.00' COMMENT '总金额',
  `platform_fee` decimal(12,2) DEFAULT '0.00' COMMENT '平台费用',
  `settlement_amount` decimal(12,2) DEFAULT '0.00' COMMENT '结算金额',
  `settlement_status` tinyint DEFAULT '1' COMMENT '结算状态：1-待结算，2-已结算',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_settlement_no` (`settlement_no`),
  KEY `idx_settlement_period` (`settlement_period`),
  KEY `idx_settlement_status` (`settlement_status`)
) ENGINE=InnoDB COMMENT='平台结算表';
```

### 11.2 合作方结算表 (partner_settlement)
```sql
CREATE TABLE `partner_settlement` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '结算ID',
  `settlement_no` varchar(50) NOT NULL COMMENT '结算单号',
  `partner_id` bigint NOT NULL COMMENT '合作方ID',
  `settlement_period` varchar(20) NOT NULL COMMENT '结算周期',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date NOT NULL COMMENT '结束日期',
  `total_orders` int DEFAULT '0' COMMENT '订单总数',
  `total_amount` decimal(12,2) DEFAULT '0.00' COMMENT '总金额',
  `deduction_amount` decimal(12,2) DEFAULT '0.00' COMMENT '扣除金额',
  `settlement_amount` decimal(12,2) DEFAULT '0.00' COMMENT '结算金额',
  `settlement_status` tinyint DEFAULT '1' COMMENT '结算状态：1-待结算，2-已结算',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_settlement_no` (`settlement_no`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_settlement_period` (`settlement_period`)
) ENGINE=InnoDB COMMENT='合作方结算表';
```

## 12. 公共模块表设计

### 12.1 操作日志表 (operation_log)
```sql
CREATE TABLE `operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `operation` varchar(100) DEFAULT NULL COMMENT '操作内容',
  `method` varchar(200) DEFAULT NULL COMMENT '请求方法',
  `params` text COMMENT '请求参数',
  `result` text COMMENT '返回结果',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `location` varchar(100) DEFAULT NULL COMMENT '操作地点',
  `browser` varchar(100) DEFAULT NULL COMMENT '浏览器',
  `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
  `status` tinyint DEFAULT '1' COMMENT '操作状态：0-失败，1-成功',
  `error_msg` text COMMENT '错误信息',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `cost_time` bigint DEFAULT NULL COMMENT '耗时(毫秒)',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB COMMENT='操作日志表';
```

### 12.2 文件信息表 (file_info)
```sql
CREATE TABLE `file_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `original_name` varchar(200) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `file_suffix` varchar(10) DEFAULT NULL COMMENT '文件后缀',
  `upload_by` bigint DEFAULT NULL COMMENT '上传人',
  `upload_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  PRIMARY KEY (`id`),
  KEY `idx_upload_by` (`upload_by`),
  KEY `idx_upload_time` (`upload_time`)
) ENGINE=InnoDB COMMENT='文件信息表';
```

## 13. 索引设计说明

### 13.1 主键索引
所有表都使用自增主键 `id`，确保唯一性和查询性能。

### 13.2 唯一索引
- 用户表：用户名唯一
- 角色表：角色编码唯一
- 车辆表：车牌号唯一
- 司机表：手机号唯一
- 运单表：运单号唯一

### 13.3 普通索引
- 状态字段：便于按状态查询
- 时间字段：便于按时间范围查询
- 外键字段：便于关联查询
- 业务字段：便于业务查询

### 13.4 复合索引
- 用户角色关联表：(user_id, role_id)
- 角色菜单关联表：(role_id, menu_id)
- 城市区县：(city, district)

## 14. 数据库配置建议

### 14.1 字符集设置
```sql
DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
```

### 14.2 存储引擎
使用 InnoDB 存储引擎，支持事务和外键约束。

### 14.3 分区策略
对于大数据量表（如操作日志表），可考虑按时间分区。

### 14.4 备份策略
- 每日全量备份
- 实时增量备份
- 定期备份验证