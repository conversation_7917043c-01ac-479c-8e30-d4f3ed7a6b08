# 物流管理系统接口设计文档

## 1. 接口设计概述

### 1.1 设计原则
- 遵循RESTful API设计规范
- 统一的请求响应格式
- 完善的错误处理机制
- 支持分页查询
- 接口版本控制

### 1.2 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 1.3 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "current": 1,
    "size": 10,
    "total": 100,
    "records": []
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 2. 系统管理模块接口

### 2.1 用户管理接口

#### 2.1.1 获取用户列表
```
GET /api/v1/system/user/list
```

**请求参数：**
```json
{
  "current": 1,
  "size": 10,
  "username": "admin",
  "realName": "管理员",
  "status": 1,
  "orgId": 1
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "current": 1,
    "size": 10,
    "total": 50,
    "records": [
      {
        "id": 1,
        "username": "admin",
        "realName": "管理员",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "status": 1,
        "orgId": 1,
        "orgName": "总公司",
        "createTime": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 2.1.2 创建用户
```
POST /api/v1/system/user
```

**请求体：**
```json
{
  "username": "testuser",
  "password": "123456",
  "realName": "测试用户",
  "phone": "***********",
  "email": "<EMAIL>",
  "orgId": 1,
  "roleIds": [1, 2],
  "remark": "测试用户"
}
```

#### 2.1.3 更新用户
```
PUT /api/v1/system/user/{id}
```

#### 2.1.4 删除用户
```
DELETE /api/v1/system/user/{id}
```

#### 2.1.5 重置密码
```
PUT /api/v1/system/user/{id}/reset-password
```

### 2.2 角色管理接口

#### 2.2.1 获取角色列表
```
GET /api/v1/system/role/list
```

#### 2.2.2 创建角色
```
POST /api/v1/system/role
```

**请求体：**
```json
{
  "roleName": "调度员",
  "roleCode": "dispatcher",
  "description": "负责货物调度",
  "dataScope": 2,
  "menuIds": [1, 2, 3],
  "sortOrder": 1
}
```

#### 2.2.3 更新角色
```
PUT /api/v1/system/role/{id}
```

#### 2.2.4 删除角色
```
DELETE /api/v1/system/role/{id}
```

#### 2.2.5 分配菜单权限
```
PUT /api/v1/system/role/{id}/menus
```

### 2.3 菜单管理接口

#### 2.3.1 获取菜单树
```
GET /api/v1/system/menu/tree
```

#### 2.3.2 创建菜单
```
POST /api/v1/system/menu
```

**请求体：**
```json
{
  "parentId": 0,
  "menuName": "系统管理",
  "menuCode": "system",
  "menuType": 1,
  "path": "/system",
  "component": "Layout",
  "icon": "system",
  "sortOrder": 1,
  "visible": 1,
  "status": 1
}
```

#### 2.3.3 更新菜单
```
PUT /api/v1/system/menu/{id}
```

#### 2.3.4 删除菜单
```
DELETE /api/v1/system/menu/{id}
```

### 2.4 组织管理接口

#### 2.4.1 获取组织树
```
GET /api/v1/system/org/tree
```

#### 2.4.2 创建组织
```
POST /api/v1/system/org
```

#### 2.4.3 更新组织
```
PUT /api/v1/system/org/{id}
```

#### 2.4.4 删除组织
```
DELETE /api/v1/system/org/{id}
```

### 2.5 字典管理接口

#### 2.5.1 获取字典列表
```
GET /api/v1/system/dict/list
```

#### 2.5.2 获取字典项
```
GET /api/v1/system/dict/{dictCode}/items
```

#### 2.5.3 创建字典
```
POST /api/v1/system/dict
```

#### 2.5.4 创建字典项
```
POST /api/v1/system/dict/{dictId}/items
```

## 3. 移动管理模块接口

### 3.1 移动端菜单管理接口

#### 3.1.1 获取移动端菜单树
```
GET /api/v1/mobile/menu/tree
```

#### 3.1.2 创建移动端菜单
```
POST /api/v1/mobile/menu
```

#### 3.1.3 更新移动端菜单
```
PUT /api/v1/mobile/menu/{id}
```

#### 3.1.4 删除移动端菜单
```
DELETE /api/v1/mobile/menu/{id}
```

### 3.2 移动端功能管理接口

#### 3.2.1 获取功能列表
```
GET /api/v1/mobile/function/list
```

#### 3.2.2 创建功能
```
POST /api/v1/mobile/function
```

## 4. 运力管理模块接口

### 4.1 车辆管理接口

#### 4.1.1 获取车辆列表
```
GET /api/v1/capacity/vehicle/list
```

**请求参数：**
```json
{
  "current": 1,
  "size": 10,
  "licensePlate": "京A12345",
  "vehicleType": 1,
  "partnerId": 1,
  "status": 1
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "current": 1,
    "size": 10,
    "total": 20,
    "records": [
      {
        "id": 1,
        "licensePlate": "京A12345",
        "vehicleType": 1,
        "vehicleModel": "解放J6",
        "loadCapacity": 10.5,
        "volumeCapacity": 45.0,
        "vehicleLength": 9.6,
        "vehicleWidth": 2.4,
        "vehicleHeight": 2.8,
        "partnerId": null,
        "partnerName": null,
        "status": 1,
        "createTime": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 4.1.2 创建车辆
```
POST /api/v1/capacity/vehicle
```

**请求体：**
```json
{
  "licensePlate": "京A12345",
  "vehicleType": 1,
  "vehicleModel": "解放J6",
  "loadCapacity": 10.5,
  "volumeCapacity": 45.0,
  "vehicleLength": 9.6,
  "vehicleWidth": 2.4,
  "vehicleHeight": 2.8,
  "partnerId": null,
  "remark": "自有车辆"
}
```

#### 4.1.3 更新车辆
```
PUT /api/v1/capacity/vehicle/{id}
```

#### 4.1.4 删除车辆
```
DELETE /api/v1/capacity/vehicle/{id}
```

#### 4.1.5 获取车辆详情
```
GET /api/v1/capacity/vehicle/{id}
```

### 4.2 司机管理接口

#### 4.2.1 获取司机列表
```
GET /api/v1/capacity/driver/list
```

**请求参数：**
```json
{
  "current": 1,
  "size": 10,
  "driverName": "张三",
  "phone": "13800138000",
  "driverType": 1,
  "partnerId": 1,
  "status": 1
}
```

#### 4.2.2 创建司机
```
POST /api/v1/capacity/driver
```

**请求体：**
```json
{
  "driverName": "张三",
  "phone": "13800138000",
  "idCard": "110101199001011234",
  "licenseNumber": "110101199001011234",
  "licenseType": "A2",
  "licenseExpireDate": "2025-12-31",
  "driverType": 1,
  "partnerId": null,
  "remark": "自有司机"
}
```

#### 4.2.3 更新司机
```
PUT /api/v1/capacity/driver/{id}
```

#### 4.2.4 删除司机
```
DELETE /api/v1/capacity/driver/{id}
```

### 4.3 合作方账户管理接口

#### 4.3.1 获取合作方列表
```
GET /api/v1/capacity/partner/list
```

#### 4.3.2 创建合作方账户
```
POST /api/v1/capacity/partner
```

**请求体：**
```json
{
  "partnerName": "顺丰速运",
  "partnerCode": "SF001",
  "contactPerson": "李四",
  "contactPhone": "***********",
  "contactEmail": "<EMAIL>",
  "address": "北京市朝阳区xxx",
  "bankName": "中国银行",
  "bankAccount": "****************",
  "taxNumber": "91110000123456789X"
}
```

#### 4.3.3 更新合作方账户
```
PUT /api/v1/capacity/partner/{id}
```

#### 4.3.4 删除合作方账户
```
DELETE /api/v1/capacity/partner/{id}
```

## 5. 调度管理模块接口

### 5.1 人车绑定接口

#### 5.1.1 获取绑定列表
```
GET /api/v1/dispatch/bind/list
```

#### 5.1.2 创建人车绑定
```
POST /api/v1/dispatch/bind
```

**请求体：**
```json
{
  "driverId": 1,
  "vehicleId": 1,
  "bindDate": "2024-01-01"
}
```

#### 5.1.3 解除人车绑定
```
PUT /api/v1/dispatch/bind/{id}/unbind
```

### 5.2 车次管理接口

#### 5.2.1 获取车次列表
```
GET /api/v1/dispatch/trip/list
```

**请求参数：**
```json
{
  "current": 1,
  "size": 10,
  "tripNo": "T202401010001",
  "driverId": 1,
  "vehicleId": 1,
  "tripStatus": 1,
  "departureCity": "北京",
  "destinationCity": "上海"
}
```

#### 5.2.2 创建车次
```
POST /api/v1/dispatch/trip
```

**请求体：**
```json
{
  "driverId": 1,
  "vehicleId": 1,
  "departureCity": "北京",
  "destinationCity": "上海",
  "departureAddress": "北京市朝阳区xxx",
  "destinationAddress": "上海市浦东新区xxx",
  "plannedDepartureTime": "2024-01-01T08:00:00Z",
  "plannedArrivalTime": "2024-01-02T08:00:00Z",
  "remark": "正常车次"
}
```

#### 5.2.3 批量创建车次
```
POST /api/v1/dispatch/trip/batch
```

**请求体：**
```json
{
  "trips": [
    {
      "driverId": 1,
      "vehicleId": 1,
      "departureCity": "北京",
      "destinationCity": "上海",
      "plannedDepartureTime": "2024-01-01T08:00:00Z"
    },
    {
      "driverId": 2,
      "vehicleId": 2,
      "departureCity": "北京",
      "destinationCity": "广州",
      "plannedDepartureTime": "2024-01-01T09:00:00Z"
    }
  ]
}
```

#### 5.2.4 更新车次
```
PUT /api/v1/dispatch/trip/{id}
```

#### 5.2.5 删除车次
```
DELETE /api/v1/dispatch/trip/{id}
```

#### 5.2.6 车次发车确认
```
PUT /api/v1/dispatch/trip/{id}/departure
```

## 6. 落地接车管理模块接口

### 6.1 运单导入接口

#### 6.1.1 获取运单列表
```
GET /api/v1/arrival/waybill/list
```

**请求参数：**
```json
{
  "current": 1,
  "size": 10,
  "waybillNo": "W202401010001",
  "senderName": "张三",
  "receiverName": "李四",
  "receiverCity": "北京",
  "waybillStatus": 1,
  "sourceType": 1,
  "startTime": "2024-01-01",
  "endTime": "2024-01-31"
}
```

#### 6.1.2 手工录入运单
```
POST /api/v1/arrival/waybill
```

**请求体：**
```json
{
  "waybillNo": "W202401010001",
  "senderName": "张三",
  "senderPhone": "13800138000",
  "senderAddress": "北京市朝阳区xxx",
  "receiverName": "李四",
  "receiverPhone": "***********",
  "receiverAddress": "上海市浦东新区xxx",
  "receiverCity": "上海",
  "receiverDistrict": "浦东新区",
  "cargoName": "电子产品",
  "cargoWeight": 100.5,
  "cargoVolume": 2.5,
  "cargoQuantity": 10,
  "cargoValue": 50000.00,
  "freightAmount": 500.00,
  "paymentMethod": 1,
  "remark": "易碎品"
}
```

#### 6.1.3 Excel导入运单
```
POST /api/v1/arrival/waybill/import
```

**请求体：** multipart/form-data
- file: Excel文件

#### 6.1.4 下载导入模板
```
GET /api/v1/arrival/waybill/template
```

#### 6.1.5 更新运单
```
PUT /api/v1/arrival/waybill/{id}
```

#### 6.1.6 删除运单
```
DELETE /api/v1/arrival/waybill/{id}
```

### 6.2 TMS接口集成

#### 6.2.1 同步TMS运单
```
POST /api/v1/arrival/tms/sync
```

**请求体：**
```json
{
  "startTime": "2024-01-01T00:00:00Z",
  "endTime": "2024-01-01T23:59:59Z",
  "waybillNos": ["W001", "W002"]
}
```

#### 6.2.2 获取同步状态
```
GET /api/v1/arrival/tms/sync/status/{taskId}
```

### 6.3 单号生成接口

#### 6.3.1 生成附单号
```
POST /api/v1/arrival/serial/generate
```

**请求体：**
```json
{
  "waybillId": 1,
  "quantity": 1
}
```

### 6.4 到车确认接口

#### 6.4.1 到车确认
```
POST /api/v1/arrival/confirm
```

**请求体：**
```json
{
  "vehicleId": 1,
  "driverId": 1,
  "arrivalTime": "2024-01-01T10:00:00Z",
  "waybillIds": [1, 2, 3],
  "remark": "货物完好"
}
```

#### 6.4.2 获取到车确认列表
```
GET /api/v1/arrival/confirm/list
```

#### 6.4.3 获取货物统计
```
GET /api/v1/arrival/statistics
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalWaybills": 100,
    "totalWeight": 5000.5,
    "totalVolume": 250.0,
    "cityStatistics": [
      {
        "city": "北京",
        "waybillCount": 30,
        "weight": 1500.0,
        "volume": 75.0
      }
    ]
  }
}
```

## 7. 仓储管理模块接口

### 7.1 入库管理接口

#### 7.1.1 获取入库记录列表
```
GET /api/v1/warehouse/inbound/list
```

#### 7.1.2 创建入库记录
```
POST /api/v1/warehouse/inbound
```

#### 7.1.3 批量入库
```
POST /api/v1/warehouse/inbound/batch
```

#### 7.1.4 更新入库记录
```
PUT /api/v1/warehouse/inbound/{id}
```

### 7.2 出库管理接口

#### 7.2.1 获取出库记录列表
```
GET /api/v1/warehouse/outbound/list
```

#### 7.2.2 预出库
```
POST /api/v1/warehouse/outbound/pre
```

#### 7.2.3 正式出库
```
PUT /api/v1/warehouse/outbound/{id}/confirm
```

#### 7.2.4 取消出库
```
PUT /api/v1/warehouse/outbound/{id}/cancel
```

## 8. 货物分发模块接口

### 8.1 货物分发接口

#### 8.1.1 获取分发列表
```
GET /api/v1/distribution/list
```

#### 8.1.2 货物分发
```
POST /api/v1/distribution
```

**请求体：**
```json
{
  "inboundRecordIds": [1, 2, 3],
  "dispatcherId": 1,
  "remark": "按区域分发"
}
```

#### 8.1.3 批量分发
```
POST /api/v1/distribution/batch
```

#### 8.1.4 取消分发
```
PUT /api/v1/distribution/{id}/cancel
```

### 8.2 地址坐标服务接口

#### 8.2.1 获取地址坐标
```
POST /api/v1/distribution/geocoding
```

**请求体：**
```json
{
  "address": "北京市朝阳区xxx"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "address": "北京市朝阳区xxx",
    "longitude": 116.397128,
    "latitude": 39.916527,
    "city": "北京",
    "district": "朝阳区"
  }
}
```

#### 8.2.2 计算距离
```
POST /api/v1/distribution/distance
```

**请求体：**
```json
{
  "origin": {
    "longitude": 116.397128,
    "latitude": 39.916527
  },
  "destination": {
    "longitude": 121.473701,
    "latitude": 31.230416
  }
}
```

### 8.3 调度区域管理接口

#### 8.3.1 获取调度区域列表
```
GET /api/v1/distribution/area/list
```

#### 8.3.2 创建调度区域
```
POST /api/v1/distribution/area
```

#### 8.3.3 更新调度区域
```
PUT /api/v1/distribution/area/{id}
```

## 9. 货物配载模块接口

### 9.1 货物配载接口

#### 9.1.1 获取配载列表
```
GET /api/v1/loading/list
```

#### 9.1.2 货物配载
```
POST /api/v1/loading
```

**请求体：**
```json
{
  "tripId": 1,
  "distributionIds": [1, 2, 3],
  "remark": "同城配载"
}
```

#### 9.1.3 批量配载
```
POST /api/v1/loading/batch
```

#### 9.1.4 取消配载
```
PUT /api/v1/loading/{id}/cancel
```

#### 9.1.5 配载验证
```
POST /api/v1/loading/validate
```

**请求体：**
```json
{
  "tripId": 1,
  "distributionIds": [1, 2, 3]
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "valid": true,
    "totalWeight": 5000.0,
    "totalVolume": 25.0,
    "vehicleCapacity": {
      "loadCapacity": 10000.0,
      "volumeCapacity": 50.0
    },
    "weightExceeded": false,
    "volumeExceeded": false,
    "destinationMatched": true,
    "warnings": []
  }
}
```

### 9.2 撤单管理接口

#### 9.2.1 撤单
```
POST /api/v1/loading/cancellation
```

**请求体：**
```json
{
  "loadingId": 1,
  "cancellationReason": "运力超时未接单",
  "timeoutMinutes": 120
}
```

#### 9.2.2 获取撤单记录
```
GET /api/v1/loading/cancellation/list
```

#### 9.2.3 重新分配
```
POST /api/v1/loading/cancellation/{id}/reassign
```

## 10. 用户中心模块接口

### 10.1 认证审核接口

#### 10.1.1 获取认证申请列表
```
GET /api/v1/usercenter/authentication/list
```

**请求参数：**
```json
{
  "current": 1,
  "size": 10,
  "realName": "张三",
  "phone": "13800138000",
  "auditStatus": 0,
  "startTime": "2024-01-01",
  "endTime": "2024-01-31"
}
```

#### 10.1.2 获取认证详情
```
GET /api/v1/usercenter/authentication/{id}
```

#### 10.1.3 审核认证
```
PUT /api/v1/usercenter/authentication/{id}/audit
```

**请求体：**
```json
{
  "auditStatus": 1,
  "auditRemark": "审核通过"
}
```

#### 10.1.4 批量审核
```
PUT /api/v1/usercenter/authentication/batch-audit
```

**请求体：**
```json
{
  "ids": [1, 2, 3],
  "auditStatus": 1,
  "auditRemark": "批量审核通过"
}
```

## 11. 清分结算模块接口

### 11.1 平台结算接口

#### 11.1.1 获取平台结算列表
```
GET /api/v1/settlement/platform/list
```

#### 11.1.2 创建平台结算
```
POST /api/v1/settlement/platform
```

**请求体：**
```json
{
  "settlementPeriod": "2024-01",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

#### 11.1.3 结算确认
```
PUT /api/v1/settlement/platform/{id}/confirm
```

#### 11.1.4 导出结算单
```
GET /api/v1/settlement/platform/{id}/export
```

### 11.2 合作方结算接口

#### 11.2.1 获取合作方结算列表
```
GET /api/v1/settlement/partner/list
```

#### 11.2.2 创建合作方结算
```
POST /api/v1/settlement/partner
```

**请求体：**
```json
{
  "partnerId": 1,
  "settlementPeriod": "2024-01",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31"
}
```

#### 11.2.3 结算确认
```
PUT /api/v1/settlement/partner/{id}/confirm
```

#### 11.2.4 导出结算单
```
GET /api/v1/settlement/partner/{id}/export
```

## 12. 公共接口

### 12.1 文件管理接口

#### 12.1.1 文件上传
```
POST /api/v1/file/upload
```

**请求体：** multipart/form-data
- file: 上传的文件

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "fileId": 1,
    "fileName": "document.pdf",
    "filePath": "/uploads/2024/01/01/document.pdf",
    "fileSize": 1024000,
    "fileUrl": "https://example.com/uploads/2024/01/01/document.pdf"
  }
}
```

#### 12.1.2 文件下载
```
GET /api/v1/file/download/{fileId}
```

#### 12.1.3 文件删除
```
DELETE /api/v1/file/{fileId}
```

### 12.2 Excel导入导出接口

#### 12.2.1 Excel导出
```
POST /api/v1/excel/export
```

**请求体：**
```json
{
  "templateType": "waybill",
  "data": [],
  "fileName": "运单列表"
}
```

#### 12.2.2 获取导入模板
```
GET /api/v1/excel/template/{templateType}
```

### 12.3 操作日志接口

#### 12.3.1 获取操作日志
```
GET /api/v1/audit/log/list
```

**请求参数：**
```json
{
  "current": 1,
  "size": 10,
  "username": "admin",
  "operation": "用户管理",
  "status": 1,
  "startTime": "2024-01-01",
  "endTime": "2024-01-31"
}
```

## 13. 错误码定义

### 13.1 通用错误码
- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

### 13.2 业务错误码
- 1001: 用户名已存在
- 1002: 密码错误
- 1003: 用户已被禁用
- 2001: 车牌号已存在
- 2002: 司机手机号已存在
- 3001: 运单号已存在
- 3002: 货物已配载
- 4001: 车辆载重超限
- 4002: 目的地不匹配

## 14. 接口认证

### 14.1 JWT Token认证
所有接口（除登录接口外）都需要在请求头中携带JWT Token：

```
Authorization: Bearer <token>
```

### 14.2 权限验证
接口会根据用户角色和权限进行访问控制，无权限时返回403错误。

## 15. 接口版本控制

### 15.1 版本号规则
- v1: 第一版本
- v2: 第二版本（向后兼容）

### 15.2 版本升级策略
- 新版本保持向后兼容
- 废弃接口提前通知
- 提供版本迁移指南