# 物流管理系统模块设计文档

## 1. 系统架构概述

### 1.1 整体架构
系统采用分层架构设计，包含表现层、业务逻辑层、数据访问层和数据存储层。

### 1.2 技术栈
- 后端：Spring Boot + MyBatis Plus
- 前端：Vue.js + Element UI
- 数据库：MySQL
- 缓存：Redis
- 消息队列：RabbitMQ
- 文件存储：MinIO/阿里云OSS

## 2. 模块设计

### 2.1 系统管理模块 (system)

#### 2.1.1 菜单管理子模块
**功能描述**：管理系统菜单的层级结构和权限配置

**核心类设计**：
- `SysMenu`：菜单实体类
- `SysMenuController`：菜单控制器
- `SysMenuService`：菜单业务逻辑
- `SysMenuMapper`：菜单数据访问

**主要接口**：
- `GET /system/menu/list`：获取菜单列表
- `POST /system/menu`：新增菜单
- `PUT /system/menu/{id}`：更新菜单
- `DELETE /system/menu/{id}`：删除菜单

#### 2.1.2 字典管理子模块
**功能描述**：维护系统字典数据

**核心类设计**：
- `SysDict`：字典实体类
- `SysDictItem`：字典项实体类
- `SysDictController`：字典控制器
- `SysDictService`：字典业务逻辑

#### 2.1.3 用户管理子模块
**功能描述**：管理系统用户账号信息

**核心类设计**：
- `SysUser`：用户实体类
- `SysUserController`：用户控制器
- `SysUserService`：用户业务逻辑
- `PasswordEncoder`：密码加密工具

#### 2.1.4 角色管理子模块
**功能描述**：管理用户角色和权限分配

**核心类设计**：
- `SysRole`：角色实体类
- `SysRoleMenu`：角色菜单关联实体
- `SysUserRole`：用户角色关联实体
- `SysRoleController`：角色控制器

#### 2.1.5 组织管理子模块
**功能描述**：维护组织架构信息

**核心类设计**：
- `SysOrg`：组织实体类
- `SysOrgController`：组织控制器
- `SysOrgService`：组织业务逻辑

### 2.2 移动管理模块 (mobile)

#### 2.2.1 移动端菜单管理
**功能描述**：配置移动端菜单和功能权限

**核心类设计**：
- `MobileMenu`：移动端菜单实体
- `MobileFunction`：移动端功能实体
- `MobileMenuController`：移动端菜单控制器

### 2.3 运力管理模块 (capacity)

#### 2.3.1 车辆管理子模块
**功能描述**：管理自有和合作方车辆信息

**核心类设计**：
- `Vehicle`：车辆实体类
- `VehicleController`：车辆控制器
- `VehicleService`：车辆业务逻辑
- `VehicleTypeEnum`：车辆类型枚举（自有/合作）

**主要接口**：
- `GET /capacity/vehicle/list`：获取车辆列表
- `POST /capacity/vehicle`：新增车辆
- `PUT /capacity/vehicle/{id}`：更新车辆信息

#### 2.3.2 司机管理子模块
**功能描述**：管理司机信息和资质

**核心类设计**：
- `Driver`：司机实体类
- `DriverController`：司机控制器
- `DriverService`：司机业务逻辑
- `DriverLicense`：驾驶证信息实体

#### 2.3.3 合作方账户管理
**功能描述**：管理合作方账户信息

**核心类设计**：
- `PartnerAccount`：合作方账户实体
- `PartnerAccountController`：账户控制器
- `PartnerAccountService`：账户业务逻辑

### 2.4 调度管理模块 (dispatch)

#### 2.4.1 人车绑定子模块
**功能描述**：管理司机与车辆的绑定关系

**核心类设计**：
- `DriverVehicleBind`：人车绑定实体
- `DriverVehicleBindController`：绑定控制器
- `DriverVehicleBindService`：绑定业务逻辑

#### 2.4.2 车次管理子模块
**功能描述**：创建和管理运输车次

**核心类设计**：
- `Trip`：车次实体类
- `TripController`：车次控制器
- `TripService`：车次业务逻辑
- `BatchTripCreateService`：批量创建车次服务

**主要接口**：
- `POST /dispatch/trip`：创建单个车次
- `POST /dispatch/trip/batch`：批量创建车次
- `GET /dispatch/trip/list`：获取车次列表

### 2.5 落地接车管理模块 (arrival)

#### 2.5.1 运单导入子模块
**功能描述**：支持手工录入和Excel导入运单信息

**核心类设计**：
- `Waybill`：运单实体类
- `WaybillImportController`：运单导入控制器
- `WaybillImportService`：导入业务逻辑
- `ExcelImportUtil`：Excel导入工具类

#### 2.5.2 TMS接口集成子模块
**功能描述**：从上游TMS系统获取运单信息

**核心类设计**：
- `TmsIntegrationService`：TMS集成服务
- `TmsApiClient`：TMS API客户端
- `WaybillSyncService`：运单同步服务

#### 2.5.3 单号生成子模块
**功能描述**：生成附单单号

**核心类设计**：
- `SerialNumberGenerator`：单号生成器
- `SerialNumberService`：单号生成服务
- `SerialNumberRule`：单号规则配置

#### 2.5.4 到车确认子模块
**功能描述**：确认车辆到达并统计货物

**核心类设计**：
- `ArrivalConfirm`：到车确认实体
- `ArrivalConfirmController`：到车确认控制器
- `CargoStatisticsService`：货物统计服务

### 2.6 仓储管理模块 (warehouse)

#### 2.6.1 入库管理子模块
**功能描述**：管理货物入库信息

**核心类设计**：
- `InboundRecord`：入库记录实体
- `InboundController`：入库控制器
- `InboundService`：入库业务逻辑
- `AutoInboundService`：自动入库服务

#### 2.6.2 出库管理子模块
**功能描述**：管理货物出库信息

**核心类设计**：
- `OutboundRecord`：出库记录实体
- `OutboundController`：出库控制器
- `OutboundService`：出库业务逻辑
- `PreOutboundService`：预出库服务

### 2.7 货物分发模块 (distribution)

#### 2.7.1 货物分发子模块
**功能描述**：将货物分发至指定调度

**核心类设计**：
- `CargoDistribution`：货物分发实体
- `CargoDistributionController`：分发控制器
- `CargoDistributionService`：分发业务逻辑
- `DistributionRuleService`：分发规则服务

#### 2.7.2 地址坐标服务
**功能描述**：获取地址坐标信息

**核心类设计**：
- `AddressCoordinateService`：地址坐标服务
- `GeocodingApiClient`：地理编码API客户端
- `DistanceCalculator`：距离计算器

### 2.8 货物配载模块 (loading)

#### 2.8.1 货物配载子模块
**功能描述**：将货物配载到车次

**核心类设计**：
- `CargoLoading`：货物配载实体
- `CargoLoadingController`：配载控制器
- `CargoLoadingService`：配载业务逻辑
- `LoadingCapacityValidator`：载重验证器

#### 2.8.2 撤单管理子模块
**功能描述**：处理运力超时撤单

**核心类设计**：
- `OrderCancellation`：撤单实体
- `OrderCancellationController`：撤单控制器
- `OrderCancellationService`：撤单业务逻辑
- `TimeoutMonitorService`：超时监控服务

### 2.9 用户中心模块 (usercenter)

#### 2.9.1 认证审核子模块
**功能描述**：审核移动端用户认证申请

**核心类设计**：
- `UserAuthentication`：用户认证实体
- `AuthenticationController`：认证控制器
- `AuthenticationService`：认证业务逻辑
- `AuthenticationAuditService`：认证审核服务

### 2.10 清分结算模块 (settlement)

#### 2.10.1 平台结算子模块
**功能描述**：管理平台方结算

**核心类设计**：
- `PlatformSettlement`：平台结算实体
- `PlatformSettlementController`：平台结算控制器
- `PlatformSettlementService`：平台结算业务逻辑

#### 2.10.2 合作方结算子模块
**功能描述**：管理合作方结算

**核心类设计**：
- `PartnerSettlement`：合作方结算实体
- `PartnerSettlementController`：合作方结算控制器
- `PartnerSettlementService`：合作方结算业务逻辑

## 3. 公共模块设计

### 3.1 权限控制模块 (security)
**核心类设计**：
- `SecurityConfig`：安全配置
- `JwtTokenProvider`：JWT令牌提供者
- `PermissionInterceptor`：权限拦截器
- `DataPermissionHandler`：数据权限处理器

### 3.2 文件管理模块 (file)
**核心类设计**：
- `FileUploadController`：文件上传控制器
- `FileStorageService`：文件存储服务
- `ExcelExportUtil`：Excel导出工具

### 3.3 日志审计模块 (audit)
**核心类设计**：
- `OperationLog`：操作日志实体
- `AuditLogAspect`：审计日志切面
- `AuditLogService`：审计日志服务

### 3.4 消息通知模块 (notification)
**核心类设计**：
- `NotificationService`：通知服务
- `MessageProducer`：消息生产者
- `MessageConsumer`：消息消费者

## 4. 数据库设计要点

### 4.1 核心业务表
- `sys_user`：用户表
- `sys_role`：角色表
- `sys_menu`：菜单表
- `vehicle`：车辆表
- `driver`：司机表
- `waybill`：运单表
- `trip`：车次表
- `cargo_loading`：货物配载表
- `inbound_record`：入库记录表
- `outbound_record`：出库记录表

### 4.2 关联关系表
- `sys_user_role`：用户角色关联表
- `sys_role_menu`：角色菜单关联表
- `driver_vehicle_bind`：司机车辆绑定表

### 4.3 审计表
- `operation_log`：操作日志表
- `user_authentication`：用户认证表

## 5. 接口设计规范

### 5.1 RESTful API设计
- GET：查询操作
- POST：新增操作
- PUT：更新操作
- DELETE：删除操作

### 5.2 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 5.3 分页查询格式
```json
{
  "current": 1,
  "size": 10,
  "total": 100,
  "records": []
}
```

## 6. 部署架构

### 6.1 开发环境
- 单机部署
- 内嵌数据库

### 6.2 生产环境
- 负载均衡
- 数据库集群
- Redis集群
- 容器化部署

## 7. 监控和运维

### 7.1 系统监控
- 应用性能监控
- 数据库监控
- 服务器资源监控

### 7.2 日志管理
- 应用日志
- 访问日志
- 错误日志
- 审计日志